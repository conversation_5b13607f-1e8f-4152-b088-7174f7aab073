#include <Arduino.h>
#include <SPI.h>
#include <ADS1220_WE.h>
#include <WiFi.h>
#include <WebSocketsClient.h>
#include <ArduinoJson.h>

const char* ssid = "192 HP";
const char* password = "0983353350";
const char* ws_host = "************";
const int ws_port = 80;
const char* ws_url = "/ws";

// ===== PIN DEFINITIONS =====
#define POWER_PIN 14
#define HEATER_PIN 26
#define FAN_PIN 27
#define DAC_PIN 25  // DAC pin for ~100mV signal generation

// ===== ADS1220 SPI PIN DEFINITIONS =====
#define ADS1220_CS_PIN    5   // VSPI CS pin
#define ADS1220_DRDY_PIN  4   // Data Ready pin for conversion detection
// VSPI pins: MOSI=23, MISO=19, SCLK=18 (ESP32 default)

// ===== TIMING CONSTANTS =====
const unsigned long SENSOR_INTERVAL = 1000;       // 100ms sensor reading interval for real-time display (10Hz)
const unsigned long DATA_LOG_INTERVAL = 10000;   // 10s interval for detailed data logging and charts (0.1Hz)
const unsigned long WIFI_TIMEOUT = 30000;        // 30s WiFi connection timeout
const unsigned long RECONNECT_INTERVAL = 5000;   // 5s WebSocket reconnect interval

struct DeviceData {
  bool powerState;
  bool heaterState;
  bool fanState;
};

DeviceData deviceData = { false, false, false };
WebSocketsClient webSocket;
bool isConnected = false;
unsigned long lastSensorUpdate = 0;
unsigned long lastDataLog = 0;

// FIX RACE CONDITION: Thêm flags để tránh gọi đồng thời
bool isMeasuring = false;

// ===== ADS1220 INITIALIZATION =====
ADS1220_WE ads = ADS1220_WE(ADS1220_CS_PIN, ADS1220_DRDY_PIN);

// Hàm kiểm tra và hiệu chỉnh ADS1220
void calibrateADS1220() {
  Serial.println("[ADS1220] Bắt đầu hiệu chỉnh...");

  // Test kết nối SPI
  if (!ads.init()) {
    Serial.println("[LỖI] Không thể kết nối ADS1220 qua SPI!");
    return;
  }

  // Kiểm tra internal reference
  ads.setVRefSource(ADS1220_VREF_INT);
  ads.setCompareChannels(ADS1220_MUX_AVDD_M_AVSS_4); // Test với AVDD/4
  ads.setGain(ADS1220_GAIN_1);
  ads.setDataRate(ADS1220_DR_LVL_6); // Tốc độ cao nhất cho độ chính xác

  delay(100);
  float testVoltage = ads.getVoltage_mV();
  Serial.printf("[ADS1220] Test voltage (AVDD/4): %.3fmV (expected ~825mV)\n", testVoltage);

  if (testVoltage < 700 || testVoltage > 950) {
    Serial.println("[CẢNH BÁO] ADS1220 reference voltage không ổn định!");
  } else {
    Serial.println("[ADS1220] Internal reference OK");
  }

  Serial.println("[ADS1220] Hiệu chỉnh hoàn tất");
}

void handleWebSocketEvent(WStype_t type, uint8_t * payload, size_t length) {
  switch (type) {
    case WStype_DISCONNECTED:
      Serial.println("[WSc] Không kết nối được web!");
      isConnected = false;
      break;
    case WStype_CONNECTED:
      Serial.println("[WSc] Đã kết nối với Web!");
      isConnected = true;
      webSocket.sendTXT("{\"type\":\"esp32_connect\",\"deviceId\":\"ESP32_001\"}");
      break;
    case WStype_TEXT: {
      Serial.printf("[WSc] Received: %s\n", payload);
      StaticJsonDocument<200> doc;
      DeserializationError error = deserializeJson(doc, payload);
      if (error) {
        Serial.println("Parse error");
        return;
      }
      const char* action = doc["action"];
      Serial.printf("[WSc] Action: %s\n", action);

      if (strcmp(action, "power_on") == 0) {
        digitalWrite(POWER_PIN, HIGH);  // GPIO14 HIGH khi bật On/Off
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi bật On/Off
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi bật On/Off
        deviceData.powerState = true;
        deviceData.heaterState = false;
        deviceData.fanState = false;
        Serial.println("[WSc] Power ON - GPIO14:HIGH, GPIO26:LOW, GPIO27:LOW");
      }
      else if (strcmp(action, "power_off") == 0) {
        digitalWrite(POWER_PIN, LOW);   // GPIO14 LOW khi tắt
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi tắt
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi tắt
        deviceData.powerState = false;
        deviceData.heaterState = false;
        deviceData.fanState = false;
        Serial.println("[WSc] Power OFF - GPIO14:LOW, GPIO26:LOW, GPIO27:LOW");
      }
      else if (strcmp(action, "heater_on") == 0 && deviceData.powerState) {
        digitalWrite(POWER_PIN, HIGH);  // GPIO14 HIGH khi bật bếp
        digitalWrite(HEATER_PIN, HIGH); // GPIO26 HIGH khi bật bếp
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi bật bếp
        deviceData.heaterState = true;
        deviceData.fanState = false;
        Serial.println("[WSc] Heater ON - GPIO14:HIGH, GPIO26:HIGH, GPIO27:LOW");
      }
      else if (strcmp(action, "heater_off") == 0 && deviceData.powerState) {
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi tắt bếp
        deviceData.heaterState = false;
        Serial.println("[WSc] Heater OFF - GPIO26:LOW");
      }
      else if (strcmp(action, "fan_on") == 0 && deviceData.powerState) {
        digitalWrite(POWER_PIN, HIGH);  // GPIO14 HIGH khi bật quạt
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi bật quạt
        digitalWrite(FAN_PIN, HIGH);    // GPIO27 HIGH khi bật quạt
        deviceData.fanState = true;
        deviceData.heaterState = false;
        Serial.println("[WSc] Fan ON - GPIO14:HIGH, GPIO26:LOW, GPIO27:HIGH");
      }
      else if (strcmp(action, "fan_off") == 0 && deviceData.powerState) {
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi tắt quạt
        deviceData.fanState = false;
        Serial.println("[WSc] Fan OFF - GPIO27:LOW");
      }
      else {
        Serial.println("[WSc] Unknown action");
        return;
      }

      StaticJsonDocument<200> stateDoc;
      stateDoc["type"] = "state_update";
      stateDoc["power"] = deviceData.powerState;
      stateDoc["heater"] = deviceData.heaterState;
      stateDoc["fan"] = deviceData.fanState;

      String stateJson;
      serializeJson(stateDoc, stateJson);
      webSocket.sendTXT(stateJson);
      Serial.printf("[WSc] Sent: %s\n", stateJson.c_str());
      break;
    }
  }
}

// --- PHẦN ĐO ADC ĐÃ TỐI ƯU VỚI BỘ LỌC SỐ ---
// Bộ lọc trung bình trượt để giảm nhiễu
class MovingAverageFilter {
private:
  float* buffer;
  int size;
  int index;
  float sum;
  bool filled;

public:
  MovingAverageFilter(int filterSize) {
    size = filterSize;
    buffer = new float[size];
    index = 0;
    sum = 0;
    filled = false;
    for (int i = 0; i < size; i++) {
      buffer[i] = 0;
    }
  }

  // FIX MEMORY LEAK: Thêm destructor
  ~MovingAverageFilter() {
    delete[] buffer;
    buffer = nullptr;
  }

  float addValue(float value) {
    sum -= buffer[index];
    buffer[index] = value;
    sum += value;
    index = (index + 1) % size;
    if (index == 0) filled = true;

    return sum / (filled ? size : (index == 0 ? size : index));
  }

  // Đặt lại bộ lọc khi có thay đổi đột ngột (ví dụ: tắt/bật máy)
  void reset(float giaTriKhoiTao = 0) {
    for (int i = 0; i < size; i++) {
      buffer[i] = giaTriKhoiTao;
    }
    sum = giaTriKhoiTao * size;
    index = 0;
    filled = false;
  }
};

// Khởi tạo bộ lọc cho mỗi kênh - tối ưu cho ADS1220 24-bit
MovingAverageFilter boLocDong(8);      // Bộ lọc 8 điểm cho dòng điện (tận dụng độ phân giải cao ADS1220)
MovingAverageFilter boLocDienAp(8);    // Bộ lọc 8 điểm cho điện áp (tận dụng độ phân giải cao ADS1220)

// Hàm đo dữ liệu cảm biến ADS1220 tối ưu cho độ chính xác cao nhất
void measureSensorData(float &Uc, float &current, bool highAccuracy = true) {
  // Cấu hình tối ưu cho ADS1220 24-bit với 20 SPS và 50/60Hz rejection
  int samples, settling_delay;
  bool useOutlierRejection;

  if (highAccuracy) {
    // CHÍNH XÁC TỐI ĐA - cho ghi dữ liệu (ưu tiên độ chính xác gần đồng hồ vạn năng)
    samples = 8;                     // 8 samples với ADS1220 20 SPS = ~400ms, chính xác cao
    settling_delay = 50;             // 50ms cho ADS1220 20 SPS conversion (1/20 = 50ms)
    useOutlierRejection = true;
  } else {
    // THỜI GIAN THỰC - cho hiển thị (cân bằng tốc độ và chính xác tối ưu)
    samples = 4;                     // 4 samples = ~200ms, phù hợp real-time
    settling_delay = 50;             // Cùng settling time để consistency với 20 SPS
    useOutlierRejection = true;      // Luôn dùng outlier rejection để loại bỏ spike
  }

  // === ĐO DÒNG ĐIỆN QUA MICRO AMMETER (A0-A1) với ADS1220 ===
  // Cấu hình ADS1220 cho đo dòng điện: PGA gain 128, differential A0-A1
  ads.setCompareChannels(ADS1220_MUX_0_1);  // A0-A1 differential
  ads.setGain(ADS1220_GAIN_128);            // PGA gain 128 cho 0-250µV range
  ads.bypassPGA(false);                     // Sử dụng PGA cho độ chính xác cao
  ads.setDataRate(ADS1220_DR_LVL_6);       // Tốc độ cao nhất cho độ chính xác

  delay(10); // Thời gian ổn định sau cấu hình

  float voltageDiff = 0;
  float validSamples = 0;

  for (int i = 0; i < samples; i++) {
    // Bắt đầu conversion trong single-shot mode
    ads.start();

    // Đợi conversion ready với DRDY pin
    while(digitalRead(ADS1220_DRDY_PIN) == HIGH) {
      delayMicroseconds(100);
    }

    // Đọc voltage trực tiếp từ ADS1220 (đã convert sang mV)
    float rawValue = ads.getVoltage_mV() * 1000.0; // Convert mV to µV

    // Áp dụng outlier rejection cho dải đo thực tế
    if (useOutlierRejection) {
      // Giới hạn phù hợp với dải đo: 50µA × 5000Ω = 250µV
      if (abs(rawValue) < 300.0) { // Giới hạn ±300µV để chứa tín hiệu hợp lệ
        voltageDiff += rawValue;
        validSamples++;
      }
    } else {
      // Chế độ real-time - giới hạn rộng hơn
      if (abs(rawValue) < 400.0) { // ±400µV cho real-time
        voltageDiff += rawValue;
        validSamples++;
      }
    }
    delay(settling_delay); // Đợi conversion cycle hoàn tất
  }

  if (validSamples > 0) {
    voltageDiff /= validSamples; // Trung bình chỉ các mẫu hợp lệ
  } else {
    // Không có mẫu hợp lệ - báo lỗi và dùng giá trị 0
    Serial.println("[LỖI] Không có mẫu dòng điện hợp lệ!");
    voltageDiff = 0;
  }

  // Adaptive filtering cho thí nghiệm Curie - LUÔN FILTER DÒNG ĐIỆN
  float rawCurrent = voltageDiff;
  if (highAccuracy) {
    // Data logging: Dùng filter mạnh để giảm noise tối đa
    voltageDiff = boLocDong.addValue(voltageDiff);
  } else {
    // Real-time: DÙNG FILTER NHẸ cho dòng điện (dòng điện ít thay đổi đột ngột)
    // Exponential smoothing mạnh hơn cho dòng điện: 50% mới, 50% cũ
    static float lastCurrentSmoothed = 0;
    static bool firstCurrentSmoothRun = true;
    if (firstCurrentSmoothRun) {
      lastCurrentSmoothed = rawCurrent;
      firstCurrentSmoothRun = false;
    }
    voltageDiff = 0.5 * rawCurrent + 0.5 * lastCurrentSmoothed; // Filter mạnh hơn
    lastCurrentSmoothed = voltageDiff;
  }

  // Debug sẽ được thêm sau khi tính current

  // Shunt resistor cho micro ammeter
  // Tính toán: 50µA × 5000Ω = 250mV (< 256mV dải ADC) ✓
  const float R_SHUNT = 5000.0;  // 5kΩ shunt resistor

  // Xử lý offset và cực tính - SỬA TRIỆT ĐỂ
  float rawCurrent_uA = voltageDiff / R_SHUNT; // µA = µV / Ohm

  // OFFSET CORRECTION: Đo offset khi không có dòng (zero current)
  static float offsetCorrection = 0; // Sẽ được tính tự động
  static bool offsetCalculated = false;
  static float offsetSamples[10];
  static int offsetCount = 0;

  // Tính offset trong 10 lần đo đầu (khi chưa có dòng thực)
  if (!offsetCalculated && offsetCount < 10) {
    offsetSamples[offsetCount] = rawCurrent_uA;
    offsetCount++;
    if (offsetCount == 10) {
      // Tính offset trung bình
      float sum = 0;
      for (int i = 0; i < 10; i++) {
        sum += offsetSamples[i];
      }
      offsetCorrection = sum / 10.0;
      offsetCalculated = true;
      Serial.printf("[HIỆU CHỈNH] Offset dòng điện: %.2fµA\n", offsetCorrection);
    }
  }

  // Áp dụng hiệu chỉnh offset
  float correctedCurrent = rawCurrent_uA - offsetCorrection;

  // LOẠI BỎ tự động đảo dấu - gây nhiễu loạn
  // Chỉ báo cảnh báo nếu âm, không tự động sửa
  if (correctedCurrent < 0 && offsetCalculated && abs(correctedCurrent) > 0.01) {
    Serial.printf("[CẢNH BÁO] Dòng điện âm: %.3fµA - kiểm tra kết nối A0-A1\n", correctedCurrent);
  }

  // Validation cuối cùng - CHO PHÉP dòng âm nhỏ (có thể là offset tự nhiên)
  if (correctedCurrent < -1.0) {
    current = 0; // Chỉ chặn khi âm quá lớn (< -1µA)
    Serial.printf("[LỖI] Dòng điện âm quá lớn: %.2fµA\n", correctedCurrent);
  } else if (correctedCurrent > 100) {
    current = 100; // Giới hạn tối đa 100µA
    Serial.printf("[LỖI] Dòng điện quá lớn: %.2fµA\n", correctedCurrent);
  } else {
    current = correctedCurrent; // Chấp nhận cả dòng âm nhỏ
  }

  // Debug info chi tiết cho dòng điện ADS1220 - SO SÁNH VỚI ĐỒNG HỒ
  if (highAccuracy) {
    Serial.printf("[ADS1220] Current: Raw=%.1fµV→%.3fµA, Filtered=%.1fµV→%.3fµA, Final=%.3fµA, Samples=%d/%d\n",
                  rawCurrent, rawCurrent/R_SHUNT, voltageDiff, voltageDiff/R_SHUNT, current, (int)validSamples, samples);
    Serial.printf("[SO SÁNH] Đo được: %.3fµA | Shunt: %.0fΩ | Điện áp shunt: %.1fµV\n",
                  current, R_SHUNT, current * R_SHUNT);
  }

  // Delay tối ưu khi chuyển channel để tránh crosstalk
  delay(20); // 20ms đủ cho ADS1220 channel switching

  // === ĐO THERMOCOUPLE (A2-A3) với ADS1220 ===
  // Cấu hình ADS1220 cho đo thermocouple: PGA gain 64, differential A2-A3
  ads.setCompareChannels(ADS1220_MUX_2_3);  // A2-A3 differential
  ads.setGain(ADS1220_GAIN_64);             // PGA gain 64 cho 0-30mV range
  ads.bypassPGA(false);                     // Sử dụng PGA cho độ chính xác cao
  ads.setDataRate(ADS1220_DR_LVL_6);       // Tốc độ cao nhất cho độ chính xác

  delay(20); // Channel switching settling cho ADS1220

  float tempVoltage = 0;
  validSamples = 0;

  for (int i = 0; i < samples; i++) {
    // Bắt đầu conversion trong single-shot mode
    ads.start();

    // Đợi conversion ready với DRDY pin
    while(digitalRead(ADS1220_DRDY_PIN) == HIGH) {
      delayMicroseconds(100);
    }

    // Đọc voltage trực tiếp từ ADS1220 (đã convert sang mV)
    float rawValue = ads.getVoltage_mV(); // mV từ ADS1220

    if (useOutlierRejection) {
      // Giới hạn rộng hơn để không loại bỏ thay đổi thực của thí nghiệm
      if (rawValue >= -5.0 && rawValue <= 50.0) { // Dải rộng cho thí nghiệm Curie
        tempVoltage += rawValue;
        validSamples++;
      }
    } else {
      // Chế độ real-time - chấp nhận hầu hết giá trị để theo dõi thay đổi
      if (rawValue >= -10.0 && rawValue <= 60.0) { // Dải rất rộng
        tempVoltage += rawValue;
        validSamples++;
      }
    }
    delay(settling_delay);
  }

  if (validSamples > 0) {
    tempVoltage /= validSamples;
  } else {
    // Không có mẫu hợp lệ - báo lỗi và dùng giá trị 0
    Serial.println("[LỖI] Không có mẫu thermocouple hợp lệ!");
    tempVoltage = 0;
  }

  // Adaptive filtering với phát hiện thay đổi đột ngột
  float rawVoltage = tempVoltage;
  static float lastRawVoltage = 0.0; // FIX: Khởi tạo với giá trị constant
  static unsigned long lastMeasureTime = 0; // FIX: Khởi tạo với 0
  static bool firstRun = true;

  // Phát hiện thay đổi đột ngột dựa trên thời gian và mức độ thay đổi
  unsigned long currentTime = millis();
  unsigned long timeDiff;

  // Xử lý overflow của millis() (sau ~49 ngày)
  if (currentTime >= lastMeasureTime) {
    timeDiff = currentTime - lastMeasureTime;
  } else {
    // Overflow đã xảy ra
    timeDiff = (0xFFFFFFFF - lastMeasureTime) + currentTime + 1;
  }
  float change = abs(rawVoltage - lastRawVoltage);

  // Ngưỡng thích ứng cho reset filter - TĂNG ngưỡng để giảm reset không cần thiết
  // - Thay đổi rất lớn (>10mV): Chắc chắn là tắt/bật máy hoặc thay đổi đột ngột
  // - Thời gian dài (>60s): Có thể là drift, cần reset
  float threshold = 10.0; // 10mV - chỉ reset khi thay đổi thực sự rất lớn
  bool shouldReset = false;

  if (firstRun) {
    shouldReset = true;
    Serial.println("[RESET] First run - initializing filters");
  } else if (change > threshold) {
    shouldReset = true;
    Serial.printf("[RESET] Large change detected: %.3f→%.3fmV (Δ%.3f) - likely power cycle\n",
                  lastRawVoltage, rawVoltage, rawVoltage - lastRawVoltage);
  } else if (timeDiff > 60000 && change > 2.0) {
    shouldReset = true;
    Serial.printf("[RESET] Long-term drift detected: %.3f→%.3fmV after %lums\n",
                  lastRawVoltage, rawVoltage, timeDiff);
  }

  if (shouldReset) {
    boLocDienAp.reset(rawVoltage); // Đặt lại bộ lọc điện áp với giá trị hiện tại
    boLocDong.reset(0); // Đặt lại bộ lọc dòng điện
    firstRun = false;
  }

  lastMeasureTime = currentTime;

  if (highAccuracy) {
    // Data logging: Dùng filter mạnh để giảm noise tối đa
    Uc = boLocDienAp.addValue(tempVoltage);
  } else {
    // Real-time: DÙNG FILTER MẠNH HỚN để giảm nhiễu lớn
    // Exponential smoothing: 40% mới, 60% cũ - ưu tiên ổn định hơn tốc độ
    static float lastSmoothed = 0;
    static bool firstSmoothRun = true;
    if (firstSmoothRun) {
      lastSmoothed = rawVoltage;
      firstSmoothRun = false;
    }
    Uc = 0.4 * rawVoltage + 0.6 * lastSmoothed; // Filter mạnh hơn để giảm nhiễu
    lastSmoothed = Uc;
  }

  lastRawVoltage = rawVoltage;

  // Debug info cho thermocouple ADS1220 - theo dõi thay đổi
  if (highAccuracy) {
    Serial.printf("[ADS1220] Thermocouple: Raw=%.3fmV, Filtered=%.3fmV, Samples=%d/%d\n",
                  rawVoltage, Uc, (int)validSamples, samples);
  } else {
    // Real-time debug - hiển thị thay đổi
    static float lastRaw = 0;
    static bool firstDebugRun = true;
    if (firstDebugRun) {
      lastRaw = rawVoltage;
      firstDebugRun = false;
    }
    float change = rawVoltage - lastRaw;
    if (abs(change) > 0.1) { // Tăng ngưỡng lên 0.1mV để giảm spam debug
      Serial.printf("[ADS1220_THAY_DOI] Raw: %.3f→%.3fmV (Δ%.3f), Hiển thị: %.3fmV\n",
                    lastRaw, rawVoltage, change, Uc);
    }
    lastRaw = rawVoltage;
  }

  // === PHẦN ĐO 4-5V (COMMENT) ===
  // // Đo điện áp dải 4-5V: A2=dương, A3=âm
  // float Uc = 0;
  // for (int i = 0; i < samples; i++) {
  //   int16_t diff_2_3 = ads.readADC_Differential_2_3(); // A2 - A3 differential
  //   Uc += diff_2_3 * 0.125; // mV với GAIN_ONE (125µV/bit)
  //   delay(5);
  // }
  // Uc /= samples; // Trung bình mV
  // Uc = Uc / 1000.0; // Chuyển từ mV sang V để hiển thị
}

// Gửi dữ liệu real-time cho index (cập nhật liên tục)
void sendRealtimeData() {
  if (!isConnected || !deviceData.powerState || isMeasuring) return;

  // FIX RACE CONDITION: Set flag để tránh gọi đồng thời
  isMeasuring = true;

  // Đảm bảo đo xong mới gửi - không có race condition
  float Uc, current;
  measureSensorData(Uc, current, false); // false = real-time (không filter, thay đổi tức thì)

  // Kiểm tra dữ liệu hợp lệ trước khi gửi
  if (isnan(Uc) || isnan(current)) {
    Serial.println("[LỖI] Real-time data invalid - skipping send");
    return;
  }

  unsigned long recordTime = millis();

  StaticJsonDocument<200> doc;
  doc["type"] = "realtime_data";  // Loại dữ liệu real-time
  doc["Uc"] = Uc;
  doc["I"] = current;
  doc["heater"] = deviceData.heaterState;
  doc["fan"] = deviceData.fanState;
  doc["recordTime"] = recordTime;

  String json;
  size_t jsonSize = serializeJson(doc, json);
  if (jsonSize > 0) {
    webSocket.sendTXT(json);
    Serial.printf("[Real-time 10Hz] Uc=%.3fmV, I=%.3fuA at %lu ms (JSON: %d bytes)\n",
                  Uc, current, recordTime, jsonSize);
  } else {
    Serial.println("[LỖI] Không thể tạo JSON cho real-time data!");
  }

  // FIX RACE CONDITION: Reset flag
  isMeasuring = false;
}

// Gửi dữ liệu chi tiết cho logging và đồ thị (mỗi 10s)
void sendDataLog() {
  if (!isConnected || !deviceData.powerState || isMeasuring) return;

  // FIX RACE CONDITION: Set flag để tránh gọi đồng thời
  isMeasuring = true;

  // Đảm bảo đo xong mới gửi - không có race condition
  float Uc, current;
  measureSensorData(Uc, current, true); // true = chính xác cao (có filter)

  // Kiểm tra dữ liệu hợp lệ trước khi gửi
  if (isnan(Uc) || isnan(current)) {
    Serial.println("[LỖI] Data log invalid - skipping send");
    return;
  }

  unsigned long recordTime = millis();

  StaticJsonDocument<200> doc;
  doc["type"] = "data_log";  // Loại dữ liệu cho chi tiết và đồ thị
  doc["Uc"] = Uc;
  doc["I"] = current;
  doc["heater"] = deviceData.heaterState;
  doc["fan"] = deviceData.fanState;
  doc["recordTime"] = recordTime;

  String json;
  size_t jsonSize = serializeJson(doc, json);
  if (jsonSize > 0) {
    webSocket.sendTXT(json);
    Serial.printf("[Data Log 0.1Hz] Uc=%.3fmV, I=%.3fuA logged at %lu ms (JSON: %d bytes)\n",
                  Uc, current, recordTime, jsonSize);
  } else {
    Serial.println("[LỖI] Không thể tạo JSON cho data log!");
  }

  // FIX RACE CONDITION: Reset flag
  isMeasuring = false;
}



void setup() {
  Serial.begin(115200);

  pinMode(POWER_PIN, OUTPUT);
  pinMode(HEATER_PIN, OUTPUT);
  pinMode(FAN_PIN, OUTPUT);
  // Khởi tạo tất cả GPIO ở LOW (trạng thái ban đầu tắt)
  digitalWrite(POWER_PIN, LOW);
  digitalWrite(HEATER_PIN, LOW);
  digitalWrite(FAN_PIN, LOW);

  // Cấu hình DAC cho chân GPIO25: tạo tín hiệu khoảng 100 mV
  dacWrite(DAC_PIN, 8); // Tính theo: 3.3V * 8/255 ≈ 0.1035V

  // Khởi tạo SPI cho ADS1220
  pinMode(ADS1220_DRDY_PIN, INPUT);

  if (!ads.init()) {
    Serial.println("Lỗi khởi tạo ADS1220!");
    while (1);
  }

  // Cấu hình ADS1220 cho thí nghiệm Curie
  ads.setVRefSource(ADS1220_VREF_INT);          // Sử dụng internal 2.048V reference
  ads.setDataRate(ADS1220_DR_LVL_6);           // Tốc độ cao nhất cho độ chính xác
  ads.setOperatingMode(ADS1220_NORMAL_MODE);    // Normal mode cho độ chính xác cao
  ads.setConversionMode(ADS1220_SINGLE_SHOT);   // Single conversion mode

  Serial.println("[ADS1220] Khởi tạo thành công!");
  Serial.println("[ADS1220] Cấu hình: 20 SPS, Internal 2.048V ref, Single-shot mode");
  Serial.println("[ADS1220] Current: A0-A1, PGA gain 128 (±16mV range)");
  Serial.println("[ADS1220] Thermocouple: A2-A3, PGA gain 64 (±32mV range)");

  // Chạy hiệu chỉnh ADS1220
  calibrateADS1220();
  

  // Đặt lại bộ lọc khi khởi động
  boLocDong.reset(0);
  boLocDienAp.reset(0);
  Serial.println("[KHOI_DONG] Đã đặt lại bộ lọc khi khởi động");
  WiFi.begin(ssid, password);
  Serial.print("Đang kết nối WiFi...");
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\nĐã kết nối WiFi!");

  webSocket.begin(ws_host, ws_port, ws_url);
  webSocket.onEvent(handleWebSocketEvent);
  webSocket.setReconnectInterval(5000);
}

void loop() {
  // Kiểm tra heap memory để phát hiện memory leak
  static unsigned long lastMemoryCheck = 0;
  unsigned long currentTime = millis();

  if (currentTime - lastMemoryCheck >= 30000) { // Kiểm tra mỗi 30s
    size_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 10000) { // Cảnh báo nếu heap < 10KB
      Serial.printf("[CẢNH BÁO] Heap thấp: %d bytes\n", freeHeap);
    }
    lastMemoryCheck = currentTime;
  }

  webSocket.loop();

  if (isConnected && deviceData.powerState) {
    // Gửi dữ liệu khi có nguồn, không cần bật bếp/quạt

    // Gửi dữ liệu real-time mỗi 100ms (10Hz) cho index
    if (currentTime - lastSensorUpdate >= SENSOR_INTERVAL) {
      sendRealtimeData();
      lastSensorUpdate = currentTime;
    }

    // Gửi dữ liệu chi tiết mỗi 10s (0.1Hz) cho logging và đồ thị
    if (currentTime - lastDataLog >= DATA_LOG_INTERVAL) {
      sendDataLog();
      lastDataLog = currentTime;
    }
  }
}
