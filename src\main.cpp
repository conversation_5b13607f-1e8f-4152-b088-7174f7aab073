#include <Arduino.h>
#include <SPI.h>
#include <ADS1220_WE.h>
#include <WiFi.h>
#include <WebSocketsClient.h>
#include <ArduinoJson.h>

const char* ssid = "192 HP";
const char* password = "0983353350";
const char* ws_host = "************";
const int ws_port = 80;
const char* ws_url = "/ws";

// ===== PIN DEFINITIONS =====
#define POWER_PIN 14
#define HEATER_PIN 26
#define FAN_PIN 27
#define DAC_PIN 25  // DAC pin for ~100mV signal generation

// ===== ADS1220 SPI PIN DEFINITIONS =====
#define ADS1220_CS_PIN    5   // VSPI CS pin
#define ADS1220_DRDY_PIN  4   // Data Ready pin for conversion detection
// VSPI pins: MOSI=23, MISO=19, SCLK=18 (ESP32 default)

// ===== TIMING CONSTANTS =====
const unsigned long SENSOR_INTERVAL = 1000;       // 100ms sensor reading interval for real-time display (10Hz)
const unsigned long DATA_LOG_INTERVAL = 10000;   // 10s interval for detailed data logging and charts (0.1Hz)
const unsigned long WIFI_TIMEOUT = 30000;        // 30s WiFi connection timeout
const unsigned long RECONNECT_INTERVAL = 5000;   // 5s WebSocket reconnect interval

struct DeviceData {
  bool powerState;
  bool heaterState;
  bool fanState;
};

DeviceData deviceData = { false, false, false };
WebSocketsClient webSocket;
bool isConnected = false;
unsigned long lastSensorUpdate = 0;
unsigned long lastDataLog = 0;

// FIX RACE CONDITION: Thêm flags để tránh gọi đồng thời
bool isMeasuring = false;

// ===== ADS1220 INITIALIZATION =====
ADS1220_WE ads = ADS1220_WE(ADS1220_CS_PIN, ADS1220_DRDY_PIN);

// Hàm kiểm tra và hiệu chỉnh ADS1220
void calibrateADS1220() {
  Serial.println("[ADS1220] Bắt đầu hiệu chỉnh...");

  // Test kết nối SPI
  if (!ads.init()) {
    Serial.println("[LỖI] Không thể kết nối ADS1220 qua SPI!");
    return;
  }

  // Kiểm tra internal reference
  ads.setVRefSource(ADS1220_VREF_INT);
  ads.setCompareChannels(ADS1220_MUX_AVDD_M_AVSS_4); // Test với AVDD/4
  ads.setGain(ADS1220_GAIN_1);
  ads.setDataRate(ADS1220_DR_LVL_6); // Tốc độ cao nhất cho độ chính xác

  delay(100);
  float testVoltage = ads.getVoltage_mV();
  Serial.printf("[ADS1220] Test voltage (AVDD/4): %.3fmV (expected ~825mV)\n", testVoltage);

  if (testVoltage < 700 || testVoltage > 950) {
    Serial.println("[CẢNH BÁO] ADS1220 reference voltage không ổn định!");
  } else {
    Serial.println("[ADS1220] Internal reference OK");
  }

  Serial.println("[ADS1220] Hiệu chỉnh hoàn tất");
}

void handleWebSocketEvent(WStype_t type, uint8_t * payload, size_t length) {
  switch (type) {
    case WStype_DISCONNECTED:
      Serial.println("[WSc] Không kết nối được web!");
      isConnected = false;
      break;
    case WStype_CONNECTED:
      Serial.println("[WSc] Đã kết nối với Web!");
      isConnected = true;
      webSocket.sendTXT("{\"type\":\"esp32_connect\",\"deviceId\":\"ESP32_001\"}");
      break;
    case WStype_TEXT: {
      Serial.printf("[WSc] Received: %s\n", payload);
      StaticJsonDocument<200> doc;
      DeserializationError error = deserializeJson(doc, payload);
      if (error) {
        Serial.println("Parse error");
        return;
      }
      const char* action = doc["action"];
      Serial.printf("[WSc] Action: %s\n", action);

      if (strcmp(action, "power_on") == 0) {
        digitalWrite(POWER_PIN, HIGH);  // GPIO14 HIGH khi bật On/Off
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi bật On/Off
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi bật On/Off
        deviceData.powerState = true;
        deviceData.heaterState = false;
        deviceData.fanState = false;
        Serial.println("[WSc] Power ON - GPIO14:HIGH, GPIO26:LOW, GPIO27:LOW");
      }
      else if (strcmp(action, "power_off") == 0) {
        digitalWrite(POWER_PIN, LOW);   // GPIO14 LOW khi tắt
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi tắt
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi tắt
        deviceData.powerState = false;
        deviceData.heaterState = false;
        deviceData.fanState = false;
        Serial.println("[WSc] Power OFF - GPIO14:LOW, GPIO26:LOW, GPIO27:LOW");
      }
      else if (strcmp(action, "heater_on") == 0 && deviceData.powerState) {
        digitalWrite(POWER_PIN, HIGH);  // GPIO14 HIGH khi bật bếp
        digitalWrite(HEATER_PIN, HIGH); // GPIO26 HIGH khi bật bếp
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi bật bếp
        deviceData.heaterState = true;
        deviceData.fanState = false;
        Serial.println("[WSc] Heater ON - GPIO14:HIGH, GPIO26:HIGH, GPIO27:LOW");
      }
      else if (strcmp(action, "heater_off") == 0 && deviceData.powerState) {
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi tắt bếp
        deviceData.heaterState = false;
        Serial.println("[WSc] Heater OFF - GPIO26:LOW");
      }
      else if (strcmp(action, "fan_on") == 0 && deviceData.powerState) {
        digitalWrite(POWER_PIN, HIGH);  // GPIO14 HIGH khi bật quạt
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi bật quạt
        digitalWrite(FAN_PIN, HIGH);    // GPIO27 HIGH khi bật quạt
        deviceData.fanState = true;
        deviceData.heaterState = false;
        Serial.println("[WSc] Fan ON - GPIO14:HIGH, GPIO26:LOW, GPIO27:HIGH");
      }
      else if (strcmp(action, "fan_off") == 0 && deviceData.powerState) {
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi tắt quạt
        deviceData.fanState = false;
        Serial.println("[WSc] Fan OFF - GPIO27:LOW");
      }
      else {
        Serial.println("[WSc] Unknown action");
        return;
      }

      StaticJsonDocument<200> stateDoc;
      stateDoc["type"] = "state_update";
      stateDoc["power"] = deviceData.powerState;
      stateDoc["heater"] = deviceData.heaterState;
      stateDoc["fan"] = deviceData.fanState;

      String stateJson;
      serializeJson(stateDoc, stateJson);
      webSocket.sendTXT(stateJson);
      Serial.printf("[WSc] Sent: %s\n", stateJson.c_str());
      break;
    }
  }
}

// --- PHẦN ĐO ADC KHÔNG BỘ LỌC - CHỈ PHẦN CỨNG ADS1220 ---

// Hàm đo dữ liệu cảm biến ADS1220 - DỮ LIỆU THÔ KHÔNG LỌC
void measureSensorData(float &Uc, float &current, bool highAccuracy = true) {
  // Chỉ đo 1 lần - không lặp, không lọc
  int samples = 1;
  int settling_delay = 100; // 100ms để đảm bảo conversion hoàn tất

  // === ĐO DÒNG ĐIỆN QUA SHUNT RESISTOR (A0-A1) với ADS1220 ===
  // Cấu hình ADS1220 cho đo dòng điện: gain cao cho tín hiệu nhỏ
  ads.setCompareChannels(ADS1220_MUX_0_1);  // A0-A1 differential
  ads.setGain(ADS1220_GAIN_128);            // Gain 128 cho tín hiệu µV
  ads.bypassPGA(false);                     // Sử dụng PGA
  ads.setDataRate(ADS1220_DR_LVL_3);       // 20 SPS cho độ chính xác + 50/60Hz rejection

  delay(50); // Settling time

  // Đo nhiều lần để tăng độ chính xác
  float currentReadings[5];
  for (int i = 0; i < 5; i++) {
    ads.start();
    while(digitalRead(ADS1220_DRDY_PIN) == HIGH) {
      delayMicroseconds(100);
    }
    currentReadings[i] = ads.getVoltage_mV() * 1000.0; // Convert mV to µV
    delay(10);
  }

  // Tính trung bình điện áp qua shunt
  float sumCurrent = 0;
  for (int i = 0; i < 5; i++) {
    sumCurrent += currentReadings[i];
  }
  float shuntVoltage_uV = sumCurrent / 5.0;

  // Tính dòng điện: I = V / R
  const float R_SHUNT = 5000.0;  // 5kΩ shunt resistor
  float measuredCurrent = shuntVoltage_uV / R_SHUNT; // µA

  // Debug chi tiết cho dòng điện
  Serial.printf("[CURRENT_DETAIL] 5 readings: %.1f, %.1f, %.1f, %.1f, %.1f µV\n",
                currentReadings[0], currentReadings[1], currentReadings[2],
                currentReadings[3], currentReadings[4]);
  Serial.printf("[CURRENT_CALC] Shunt: %.1fµV / %.0fΩ = %.3fµA\n",
                shuntVoltage_uV, R_SHUNT, measuredCurrent);

  current = measuredCurrent;

  // Delay tối ưu khi chuyển channel để tránh crosstalk
  delay(20); // 20ms đủ cho ADS1220 channel switching

  // === ĐO ĐIỆN ÁP (A2-A3) với ADS1220 - TĂNG ĐỘ CHÍNH XÁC ===
  // Cấu hình ADS1220 cho đo điện áp: thử nhiều gain khác nhau
  ads.setCompareChannels(ADS1220_MUX_2_3);  // A2-A3 differential

  // THỬ GAIN THẤP HỚN để đo dải rộng hơn (nếu tín hiệu thực tế ~10mV)
  ads.setGain(ADS1220_GAIN_1);              // Gain 1 cho dải ±2.048V
  ads.bypassPGA(false);                     // Sử dụng PGA
  ads.setDataRate(ADS1220_DR_LVL_3);       // 20 SPS cho độ chính xác cao + 50/60Hz rejection

  delay(50); // Thời gian settling dài hơn cho độ chính xác

  // Đo nhiều lần để kiểm tra tính ổn định
  float readings[5];
  for (int i = 0; i < 5; i++) {
    // Bắt đầu conversion trong single-shot mode
    ads.start();

    // Đợi conversion ready với DRDY pin
    while(digitalRead(ADS1220_DRDY_PIN) == HIGH) {
      delayMicroseconds(100);
    }

    readings[i] = ads.getVoltage_mV(); // mV từ ADS1220
    delay(10); // Delay giữa các lần đo
  }

  // Tính trung bình và độ lệch chuẩn
  float sum = 0;
  for (int i = 0; i < 5; i++) {
    sum += readings[i];
  }
  float tempVoltage = sum / 5.0;

  // Tính độ lệch chuẩn để đánh giá nhiễu
  float variance = 0;
  for (int i = 0; i < 5; i++) {
    variance += (readings[i] - tempVoltage) * (readings[i] - tempVoltage);
  }
  float stddev = sqrt(variance / 5.0);

  // Debug chi tiết
  Serial.printf("[ADS1220_DETAIL] 5 readings: %.3f, %.3f, %.3f, %.3f, %.3f mV\n",
                readings[0], readings[1], readings[2], readings[3], readings[4]);
  Serial.printf("[ADS1220_STATS] Trung bình: %.3fmV, Độ lệch chuẩn: %.3fmV\n", tempVoltage, stddev);

  // XÓA TẤT CẢ LOGIC PHÁT HIỆN THAY ĐỔI VÀ RESET FILTER
  float rawVoltage = tempVoltage;

  // XÓA TẤT CẢ BỘ LỌC ĐIỆN ÁP - DỮ LIỆU THÔ
  Uc = tempVoltage; // Giữ nguyên dữ liệu thô từ ADS1220

  // Debug info cho thermocouple ADS1220 - DỮ LIỆU THÔ
  if (highAccuracy) {
    Serial.printf("[ADS1220] Thermocouple RAW: %.3fmV\n", rawVoltage);
  } else {
    // Real-time debug - hiển thị dữ liệu thô
    Serial.printf("[ADS1220_RAW] Thermocouple: %.3fmV\n", rawVoltage);
  }

  // === PHẦN ĐO 4-5V (COMMENT) ===
  // // Đo điện áp dải 4-5V: A2=dương, A3=âm
  // float Uc = 0;
  // for (int i = 0; i < samples; i++) {
  //   int16_t diff_2_3 = ads.readADC_Differential_2_3(); // A2 - A3 differential
  //   Uc += diff_2_3 * 0.125; // mV với GAIN_ONE (125µV/bit)
  //   delay(5);
  // }
  // Uc /= samples; // Trung bình mV
  // Uc = Uc / 1000.0; // Chuyển từ mV sang V để hiển thị
}

// Gửi dữ liệu real-time cho index (cập nhật liên tục)
void sendRealtimeData() {
  if (!isConnected || !deviceData.powerState || isMeasuring) return;

  // FIX RACE CONDITION: Set flag để tránh gọi đồng thời
  isMeasuring = true;

  // Đảm bảo đo xong mới gửi - không có race condition
  float Uc, current;
  measureSensorData(Uc, current, false); // false = real-time (không filter, thay đổi tức thì)

  // Kiểm tra dữ liệu hợp lệ trước khi gửi
  if (isnan(Uc) || isnan(current)) {
    Serial.println("[LỖI] Real-time data invalid - skipping send");
    return;
  }

  unsigned long recordTime = millis();

  StaticJsonDocument<200> doc;
  doc["type"] = "realtime_data";  // Loại dữ liệu real-time
  doc["Uc"] = Uc;
  doc["I"] = current;
  doc["heater"] = deviceData.heaterState;
  doc["fan"] = deviceData.fanState;
  doc["recordTime"] = recordTime;

  String json;
  size_t jsonSize = serializeJson(doc, json);
  if (jsonSize > 0) {
    webSocket.sendTXT(json);
    Serial.printf("[Real-time 10Hz] Uc=%.3fmV, I=%.3fuA at %lu ms (JSON: %d bytes)\n",
                  Uc, current, recordTime, jsonSize);
  } else {
    Serial.println("[LỖI] Không thể tạo JSON cho real-time data!");
  }

  // FIX RACE CONDITION: Reset flag
  isMeasuring = false;
}

// Gửi dữ liệu chi tiết cho logging và đồ thị (mỗi 10s)
void sendDataLog() {
  if (!isConnected || !deviceData.powerState || isMeasuring) return;

  // FIX RACE CONDITION: Set flag để tránh gọi đồng thời
  isMeasuring = true;

  // Đảm bảo đo xong mới gửi - không có race condition
  float Uc, current;
  measureSensorData(Uc, current, true); // true = chính xác cao (có filter)

  // Kiểm tra dữ liệu hợp lệ trước khi gửi
  if (isnan(Uc) || isnan(current)) {
    Serial.println("[LỖI] Data log invalid - skipping send");
    return;
  }

  unsigned long recordTime = millis();

  StaticJsonDocument<200> doc;
  doc["type"] = "data_log";  // Loại dữ liệu cho chi tiết và đồ thị
  doc["Uc"] = Uc;
  doc["I"] = current;
  doc["heater"] = deviceData.heaterState;
  doc["fan"] = deviceData.fanState;
  doc["recordTime"] = recordTime;

  String json;
  size_t jsonSize = serializeJson(doc, json);
  if (jsonSize > 0) {
    webSocket.sendTXT(json);
    Serial.printf("[Data Log 0.1Hz] Uc=%.3fmV, I=%.3fuA logged at %lu ms (JSON: %d bytes)\n",
                  Uc, current, recordTime, jsonSize);
  } else {
    Serial.println("[LỖI] Không thể tạo JSON cho data log!");
  }

  // FIX RACE CONDITION: Reset flag
  isMeasuring = false;
}



void setup() {
  Serial.begin(115200);

  pinMode(POWER_PIN, OUTPUT);
  pinMode(HEATER_PIN, OUTPUT);
  pinMode(FAN_PIN, OUTPUT);
  // Khởi tạo tất cả GPIO ở LOW (trạng thái ban đầu tắt)
  digitalWrite(POWER_PIN, LOW);
  digitalWrite(HEATER_PIN, LOW);
  digitalWrite(FAN_PIN, LOW);

  // Cấu hình DAC cho chân GPIO25: tạo tín hiệu khoảng 100 mV
  dacWrite(DAC_PIN, 8); // Tính theo: 3.3V * 8/255 ≈ 0.1035V

  // Khởi tạo SPI cho ADS1220
  pinMode(ADS1220_DRDY_PIN, INPUT);

  if (!ads.init()) {
    Serial.println("Lỗi khởi tạo ADS1220!");
    while (1);
  }

  // Cấu hình ADS1220 cho thí nghiệm Curie
  ads.setVRefSource(ADS1220_VREF_INT);          // Sử dụng internal 2.048V reference
  ads.setDataRate(ADS1220_DR_LVL_6);           // Tốc độ cao nhất cho độ chính xác
  ads.setOperatingMode(ADS1220_NORMAL_MODE);    // Normal mode cho độ chính xác cao
  ads.setConversionMode(ADS1220_SINGLE_SHOT);   // Single conversion mode

  Serial.println("[ADS1220] Khởi tạo thành công!");
  Serial.println("[ADS1220] Cấu hình: 20 SPS, Internal 2.048V ref, Single-shot mode");
  Serial.println("[ADS1220] Current: A0-A1, PGA gain 128 (±16mV range)");
  Serial.println("[ADS1220] Thermocouple: A2-A3, PGA gain 64 (±32mV range)");

  // Chạy hiệu chỉnh ADS1220
  calibrateADS1220();
  

  // XÓA BỘ LỌC - SỬ DỤNG DỮ LIỆU THÔ
  Serial.println("[KHOI_DONG] Sử dụng dữ liệu thô từ ADS1220 - không filter");
  WiFi.begin(ssid, password);
  Serial.print("Đang kết nối WiFi...");
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\nĐã kết nối WiFi!");

  webSocket.begin(ws_host, ws_port, ws_url);
  webSocket.onEvent(handleWebSocketEvent);
  webSocket.setReconnectInterval(5000);
}

void loop() {
  // Kiểm tra heap memory để phát hiện memory leak
  static unsigned long lastMemoryCheck = 0;
  unsigned long currentTime = millis();

  if (currentTime - lastMemoryCheck >= 30000) { // Kiểm tra mỗi 30s
    size_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 10000) { // Cảnh báo nếu heap < 10KB
      Serial.printf("[CẢNH BÁO] Heap thấp: %d bytes\n", freeHeap);
    }
    lastMemoryCheck = currentTime;
  }

  webSocket.loop();

  if (isConnected && deviceData.powerState) {
    // Gửi dữ liệu khi có nguồn, không cần bật bếp/quạt

    // Gửi dữ liệu real-time mỗi 100ms (10Hz) cho index
    if (currentTime - lastSensorUpdate >= SENSOR_INTERVAL) {
      sendRealtimeData();
      lastSensorUpdate = currentTime;
    }

    // Gửi dữ liệu chi tiết mỗi 10s (0.1Hz) cho logging và đồ thị
    if (currentTime - lastDataLog >= DATA_LOG_INTERVAL) {
      sendDataLog();
      lastDataLog = currentTime;
    }
  }
}
